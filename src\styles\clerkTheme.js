import { dark } from '@clerk/themes';

// Custom Clerk theme configuration
export const getClerkTheme = (isDark = false) => {
  const baseTheme = isDark ? dark : undefined;

  return {
    baseTheme,
    variables: {
      colorPrimary: '#0056d2',
      colorDanger: '#ff4d4f',
      colorSuccess: '#28a745',
      colorWarning: '#faad14',
      colorNeutral: isDark ? '#ffffff' : '#000000',
      colorBackground: isDark ? '#141414' : '#ffffff',
      colorInputBackground: isDark ? '#1f1f1f' : '#ffffff',
      colorInputText: isDark ? '#ffffff' : '#000000',
      colorText: isDark ? '#ffffff' : '#000000',
      colorTextSecondary: isDark ? '#a6a6a6' : '#666666',
      colorShimmer: isDark ? '#2a2a2a' : '#f0f0f0',
      borderRadius: '8px',
      fontFamily: '"poppins", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      fontSize: '14px',
      fontWeight: {
        normal: '400',
        medium: '500',
        semibold: '600',
        bold: '700',
      },
      spacingUnit: '1rem',
    },
    elements: {
      // Root container
      rootBox: {
        width: '100%',
        maxWidth: '400px',
      },

      // Card container
      card: {
        backgroundColor: isDark ? '#1f1f1f' : '#ffffff',
        border: `1px solid ${isDark ? '#303030' : '#e8e8e8'}`,
        borderRadius: '12px',
        boxShadow: isDark ? '0 4px 12px rgba(0, 0, 0, 0.3)' : '0 4px 12px rgba(0, 0, 0, 0.1)',
        padding: '2rem',
      },

      // Header elements
      headerTitle: {
        fontSize: '30px',
        fontWeight: '600',
        color: isDark ? '#ffffff' : '#000000',
        marginBottom: '0.2rem',
      },

      headerSubtitle: {
        fontSize: '14px',
        color: isDark ? '#a6a6a6' : '#666666',
        marginBottom: '0.5rem',
      },

      // Form elements
      formFieldInput: {
        backgroundColor: isDark ? '#262626' : '#ffffff',
        border: `1px solid ${isDark ? '#404040' : '#d9d9d9'}`,
        borderRadius: '8px',
        fontSize: '14px',
        color: isDark ? '#ffffff' : '#000000',
        height: '48px',
        '&:focus': {
          borderColor: '#0056d2',
          boxShadow: '0 0 0 2px rgba(0, 86, 210, 0.2)',
        },
        '&::placeholder': {
          color: isDark ? '#8c8c8c' : '#bfbfbf',
        },
      },

      formFieldLabel: {
        fontSize: '16px',
        fontWeight: '500',
        color: isDark ? '#ffffff' : '#000000',
      },

      // Buttons
      formButtonPrimary: {
        backgroundColor: '#0056d2',
        border: 'none',
        borderRadius: '8px',
        padding: '12px 24px',
        fontSize: '14px',
        fontWeight: '500',
        color: '#ffffff',
        height: '48px',
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        '&:hover': {
          backgroundColor: '#1a73e8',
          transform: 'translateY(-1px)',
        },
        '&:active': {
          backgroundColor: '#0045b2',
          transform: 'translateY(0)',
        },
        '&:disabled': {
          backgroundColor: '#fefefe',
          color: isDark ? '#8c8c8c' : '#bfbfbf',
          cursor: 'not-allowed',
          transform: 'none',
        },
      },

      // Social buttons
      socialButtonsBlockButton: {
        backgroundColor: isDark ? '#262626' : '#ffffff',
        border: `1px solid ${isDark ? '#404040' : '#d9d9d9'}`,
        borderRadius: '8px',
        padding: '12px 16px',
        fontSize: '14px',
        fontWeight: '500',
        color: isDark ? '#ffffff' : '#000000',
        height: '48px',
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        '&:hover': {
          backgroundColor: isDark ? '#303030' : '#fafafa',
          borderColor: '#0056d2',
        },
      },

      // Links
      footerActionLink: {
        color: '#0056d2',
        textDecoration: 'none',
        fontSize: '14px',
        fontWeight: '500',
        '&:hover': {
          color: '#1a73e8',
          textDecoration: 'underline',
        },
      },

      // Divider
      dividerLine: {
        backgroundColor: isDark ? '#404040' : '#e8e8e8',
      },

      dividerText: {
        color: isDark ? '#8c8c8c' : '#8c8c8c',
        fontSize: '12px',
        fontWeight: '500',
      },

      // Error messages
      formFieldErrorText: {
        color: '#ff4d4f',
        fontSize: '12px',
        marginTop: '4px',
      },

      // Loading spinner
      spinner: {
        color: '#0056d2',
      },

      // Modal overlay
      modalBackdrop: {
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
      },

      // Footer
      footer: {
        marginTop: '1.5rem',
        textAlign: 'center',
      },

      // Identity preview
      identityPreview: {
        backgroundColor: isDark ? '#262626' : '#fafafa',
        border: `1px solid ${isDark ? '#404040' : '#e8e8e8'}`,
        borderRadius: '8px',
        padding: '12px',
      },

      // Avatar
      avatarBox: {
        width: '48px',
        height: '48px',
        borderRadius: '50%',
        backgroundColor: isDark ? '#404040' : '#e8e8e8',
      },
    },
  };
};

export default getClerkTheme;
