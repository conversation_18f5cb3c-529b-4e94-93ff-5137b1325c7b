import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { ModeToggle } from "@/components/mode-toggle";

function App() {
  const [date, setDate] = React.useState<Date | undefined>(new Date());
  return (
    <>
      <h1 className="">WURK</h1>
      <Button>Button</Button>
      <Calendar
        mode="single"
        selected={date}
        onSelect={setDate}
        className="rounded-lg border"
      />
      <ModeToggle></ModeToggle>
    </>
  );
}

export default App;
